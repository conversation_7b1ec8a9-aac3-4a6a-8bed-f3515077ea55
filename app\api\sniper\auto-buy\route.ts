import { NextRequest } from 'next/server';
import { createWalletClient, http, parseEther, formatUnits, createPublicClient } from 'viem';
import { privateKeyToAccount } from 'viem/accounts';
import { getDexConfig, isChainSupported } from '../../../../lib/dexConfig';
import { SniperLogger } from '../../../../lib/sniperLogger';

// DEX Router ABI (Uniswap V2 style) - minimal for swapExactETHForTokens
const DEX_ROUTER_ABI = [
  {
    name: 'swapExactETHForTokens',
    type: 'function',
    inputs: [
      { name: 'amountOutMin', type: 'uint256' },
      { name: 'path', type: 'address[]' },
      { name: 'to', type: 'address' },
      { name: 'deadline', type: 'uint256' }
    ],
    outputs: [{ name: 'amounts', type: 'uint256[]' }],
    stateMutability: 'payable'
  },
  {
    name: 'getAmountsOut',
    type: 'function',
    inputs: [
      { name: 'amountIn', type: 'uint256' },
      { name: 'path', type: 'address[]' }
    ],
    outputs: [{ name: 'amounts', type: 'uint256[]' }],
    stateMutability: 'view'
  }
] as const;



export async function POST(request: NextRequest): Promise<Response> {
  let logger: SniperLogger | undefined;

  try {
    const { tokenAddress, amount, slippage, privateKey, chainId, sessionId, userAddress } = await request.json();

    // Create logger if session and user info provided
    if (sessionId && userAddress) {
      logger = new SniperLogger(sessionId, userAddress, chainId);
      await logger.info('AUTO_BUY_TRIGGER', `Auto-buy triggered for token: ${tokenAddress}`, {
        tokenAddress,
        metadata: {
          amount,
          slippage,
          chainId,
          hasPrivateKey: !!privateKey
        }
      });
    }

    // Validate required parameters
    if (!tokenAddress || !amount || !privateKey || !chainId) {
      const error = 'Token address, amount, private key, and chain ID are required';
      if (logger) {
        await logger.error('AUTO_BUY_ERROR', error, {
          tokenAddress,
          metadata: { missingParams: { tokenAddress: !tokenAddress, amount: !amount, privateKey: !privateKey, chainId: !chainId } }
        });
      }
      return Response.json({ error }, { status: 400 });
    }

    // Validate token address format
    if (!tokenAddress.match(/^0x[a-fA-F0-9]{40}$/)) {
      const error = 'Invalid token address format';
      if (logger) {
        await logger.error('AUTO_BUY_ERROR', error, {
          tokenAddress,
          metadata: { providedAddress: tokenAddress }
        });
      }
      return Response.json({ error }, { status: 400 });
    }

    // Validate private key format
    if (!privateKey.match(/^0x[a-fA-F0-9]{64}$/)) {
      const error = 'Invalid private key format';
      if (logger) {
        await logger.error('AUTO_BUY_ERROR', error, {
          tokenAddress,
          metadata: { privateKeyLength: privateKey.length }
        });
      }
      return Response.json({ error }, { status: 400 });
    }

    // Check if chain is supported
    if (!isChainSupported(chainId)) {
      const error = `Unsupported chain ID: ${chainId}`;
      if (logger) {
        await logger.error('AUTO_BUY_ERROR', error, {
          tokenAddress,
          metadata: { chainId, supportedChains: Object.keys(getDexConfig('25') ? ['25'] : []) }
        });
      }
      return Response.json({ error }, { status: 400 });
    }

    // Get chain configuration
    const chainConfig = getDexConfig(chainId);
    if (!chainConfig) {
      const error = `Chain configuration not found for chain ID: ${chainId}`;
      if (logger) {
        await logger.error('AUTO_BUY_ERROR', error, {
          tokenAddress,
          metadata: { chainId }
        });
      }
      return Response.json({ error }, { status: 400 });
    }

    if (logger) {
      await logger.info('AUTO_BUY_TRIGGER', 'Validation passed, proceeding with auto-buy', {
        tokenAddress,
        metadata: {
          chainConfig: {
            dexName: chainConfig.dexName,
            dexRouter: chainConfig.dexRouter,
            wrappedNative: chainConfig.wrappedNative
          },
          amount,
          slippage
        }
      });
    }

    console.log(`Auto-buy request: ${amount} native tokens for ${tokenAddress} on chain ${chainId}`);

    // Safety checks
    const amountNum = parseFloat(amount);
    if (amountNum <= 0 || amountNum > 10000) {
      return Response.json(
        { error: 'Amount must be between 0 and 1000 for safety' },
        { status: 400 }
      );
    }

    const slippageNum = parseFloat(slippage || '5');
    if (slippageNum < 0.1 || slippageNum > 80) {
      return Response.json(
        { error: 'Slippage must be between 0.1% and 80%' },
        { status: 400 }
      );
    }

    // Create account from private key
    const account = privateKeyToAccount(privateKey as `0x${string}`);
    
    // Create wallet client
    const walletClient = createWalletClient({
      account,
      chain: chainConfig.chain,
      transport: http(chainConfig.rpcUrl)
    });

    // Create public client for reading data
    const publicClient = createPublicClient({
      chain: chainConfig.chain,
      transport: http(chainConfig.rpcUrl)
    });

    // Convert amount to wei
    const amountInWei = parseEther(amount);
    
    // Set up swap path: Native -> Token
    const path = [chainConfig.wrappedNative, tokenAddress];
    
    // Calculate minimum tokens out with slippage
    const slippagePercent = parseFloat(slippage || '5');
    
    try {
      if (logger) {
        await logger.debug('AUTO_BUY_TRIGGER', 'Getting expected amounts out from DEX', {
          tokenAddress,
          metadata: {
            dexRouter: chainConfig.dexRouter,
            path,
            amountInWei: amountInWei.toString()
          }
        });
      }

      // Get expected amounts out
      const amountsOut = await publicClient.readContract({
        address: chainConfig.dexRouter as `0x${string}`,
        abi: DEX_ROUTER_ABI,
        functionName: 'getAmountsOut',
        args: [amountInWei, path as `0x${string}`[]]
      });

      const expectedTokensOut = amountsOut[1];
      const minTokensOut = (expectedTokensOut * BigInt(Math.floor((100 - slippagePercent) * 100))) / BigInt(10000);

      if (logger) {
        await logger.info('AUTO_BUY_TRIGGER', 'Calculated swap amounts', {
          tokenAddress,
          metadata: {
            expectedTokensOut: formatUnits(expectedTokensOut, 18),
            minTokensOut: formatUnits(minTokensOut, 18),
            slippagePercent,
            amountIn: amount
          }
        });
      }

      console.log(`Expected tokens out: ${formatUnits(expectedTokensOut, 18)}`);
      console.log(`Min tokens out (${slippagePercent}% slippage): ${formatUnits(minTokensOut, 18)}`);

      // Set deadline (10 minutes from now)
      const deadline = BigInt(Math.floor(Date.now() / 1000) + 600);

      if (logger) {
        await logger.info('AUTO_BUY_TRIGGER', 'Executing swap transaction', {
          tokenAddress,
          metadata: {
            router: chainConfig.dexRouter,
            deadline: deadline.toString(),
            gasLimit: '300000',
            gasPrice: chainConfig.gasPrice
          }
        });
      }

      // Execute the swap
      const txHash = await walletClient.writeContract({
        address: chainConfig.dexRouter as `0x${string}`,
        abi: DEX_ROUTER_ABI,
        functionName: 'swapExactETHForTokens',
        args: [minTokensOut, path as `0x${string}`[], account.address, deadline],
        value: amountInWei,
        gas: BigInt(300000), // Set gas limit
        gasPrice: BigInt(chainConfig.gasPrice),
        chain: chainConfig.chain
      });

      if (logger) {
        await logger.success('AUTO_BUY_SUCCESS', 'Auto-buy transaction submitted', {
          transactionHash: txHash,
          tokenAddress,
          metadata: {
            txHash,
            walletAddress: account.address
          }
        });
      }

      console.log(`Auto-buy transaction submitted: ${txHash}`);

      // Wait for transaction confirmation
      const receipt = await publicClient.waitForTransactionReceipt({
        hash: txHash,
        timeout: 60000 // 60 second timeout
      });

      if (receipt.status === 'success') {
        const result = {
          success: true,
          message: 'Auto-buy completed successfully',
          transactionHash: txHash,
          tokenAddress,
          amountIn: amount,
          expectedTokensOut: formatUnits(expectedTokensOut, 18),
          minTokensOut: formatUnits(minTokensOut, 18),
          slippage: slippagePercent,
          gasUsed: receipt.gasUsed.toString(),
          blockNumber: receipt.blockNumber.toString()
        };

        if (logger) {
          await logger.success('AUTO_BUY_SUCCESS', 'Auto-buy transaction confirmed successfully', {
            transactionHash: txHash,
            tokenAddress,
            metadata: result
          });
        }

        return Response.json(result);
      } else {
        const error = 'Transaction failed';
        if (logger) {
          await logger.error('AUTO_BUY_ERROR', error, {
            transactionHash: txHash,
            tokenAddress,
            metadata: {
              receiptStatus: receipt.status,
              gasUsed: receipt.gasUsed.toString()
            }
          });
        }

        return Response.json({
          success: false,
          error,
          transactionHash: txHash
        }, { status: 500 });
      }

    } catch (swapError) {
      console.error('Swap execution error:', swapError);

      const errorMessage = swapError instanceof Error ? swapError.message : 'Unknown error';

      if (logger) {
        await logger.error('AUTO_BUY_ERROR', 'Swap execution failed', {
          tokenAddress,
          metadata: {
            error: errorMessage,
            errorType: swapError instanceof Error ? swapError.constructor.name : 'Unknown',
            amount,
            slippage: slippagePercent
          }
        });
      }

      // Check if it's a liquidity/pair issue
      if (swapError instanceof Error && swapError.message.includes('INSUFFICIENT_OUTPUT_AMOUNT')) {
        const error = 'Insufficient liquidity or high slippage. Try increasing slippage tolerance.';
        return Response.json({
          success: false,
          error,
          details: swapError.message
        }, { status: 400 });
      }

      if (swapError instanceof Error && swapError.message.includes('INSUFFICIENT_INPUT_AMOUNT')) {
        const error = 'Insufficient input amount. Try increasing buy amount.';
        return Response.json({
          success: false,
          error,
          details: swapError.message
        }, { status: 400 });
      }

      return Response.json({
        success: false,
        error: 'Failed to execute swap',
        details: errorMessage
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Auto-buy error:', error);

    let errorMessage = 'Failed to process auto-buy request';
    if (error instanceof Error) {
      errorMessage = error.message;
    }

    if (logger) {
      await logger.error('AUTO_BUY_ERROR', 'General auto-buy error', {
        metadata: {
          error: errorMessage,
          errorType: error instanceof Error ? error.constructor.name : 'Unknown'
        }
      });
    }

    return Response.json(
      {
        error: errorMessage,
        success: false
      },
      { status: 500 }
    );
  }
}
